package adhoc.config

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import java.util.concurrent.TimeUnit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class ConfigurationServiceITSpec extends BaseAdhocITSpec {

	@Autowired
	Web3jConfig web3jConfig

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	@Override
	String getProfile() {
		return "abc"
	}

	@Autowired
	ApplicationContext applicationContext

	@Autowired
	BcmonitoringConfigurationProperties properties

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to environment-specific bucket
		def bucketName = BUCKET_PREFIX ? "${BUCKET_PREFIX}-${TEST_BUCKET}" : TEST_BUCKET
		AdhocHelper.uploadHardhatAbiFiles(s3Client, bucketName, "3000", [
			"Token",
			"Account",
			"Provider"
		])

		// Setup mock event stream and pending event
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Successful load all configuration properties correctly to run application
	 * Verifies all configuration properties correctly
	 * Expected: Service logs "Started bc monitoring"
	 */
	def "Should load all configuration properties correctly"() {
		given: "Running application with command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		when: "Command line runner is executed"
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "Configuration properties are loaded correctly"
		properties.getAws().getRegion() == "ap-northeast-1"
		properties.getAws().getAccessKeyId() == "dummy"
		properties.getAws().getSecretAccessKey() == "dummy"
		properties.getAws().getDynamodb().getRegion() == "ap-northeast-1"
		properties.getAws().getDynamodb().getEndpoint() == "http://localstack:4566"
		properties.getAws().getS3().getBucketName() == "abijson-local-bucket"
		properties.getEthereum().getEndpoint() == ""
		properties.getWebsocket().getUri().getHost() == "localhost"
		properties.getWebsocket().getUri().getPort() == "18541"
		properties.getSubscription().getCheckInterval() == "3000"
		properties.getSubscription().getAllowableBlockTimestampDiffSec() == "2"
		properties.getEnv() == "local"
		properties.getAbiFormat() == "hardhat"
		//this properties != true cuz when run application in test need set this value == false to avoid infinite loop
		!properties.isEagerStart()

		and: "Log should indicate bc monitoring has started"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
	}

	/**
	 * Successful run application with env is 'local'
	 * Verifies ENV variable value is 'local'
	 * Expected: Service logs "Started bc monitoring"
	 */
	def "Should use LocalStack endpoint when env is 'local'"() {
		given:
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		when: "Run command line with -f"
		scheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "env should be 'local'"
		properties.getEnv() == "local"

		and: "Log should indicate bc monitoring has started"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
	}

	/**
	 * Successful run application with env is 'test'
	 * Verifies ENV variable value is 'test'
	 * Expected: Service logs "Started bc monitoring"
	 */
	def "Should not use LocalStack endpoint when env is not 'local'"() {
		given:
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)
		properties.setEnv("test")

		when: "Run command line with -f"
		scheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "env should be 'test'"
		properties.getEnv() == "test"

		and: "Log should indicate bc monitoring has started"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }

		cleanup:
		// Reset the value to avoid affecting other tests
		properties.setEnv("local")
	}

	/**
	 * Successful run application with default value when optional value missing
	 * Verifies ENV variable value == default value
	 * Expected: Service logs "Started bc monitoring"
	 */
	def "Should use default values when optional env variables are missing"() {
		given: "CommandLineRunner is available"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		and: "System properties for optional env vars are cleared to simulate missing environment variables"
		System.clearProperty("ENV")

		when: "Application is started via command line runner"
		scheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "Application uses default values from application.properties"
		properties.getEnv() == "local"

		and: "Application should still start monitoring"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
	}

	/**
	 * Test configuration loading based on environment
	 * Verifies that configuration is loaded correctly for test environment
	 * Expected: Configuration matches test profile values
	 */
	def "Should load configuration based on test environment profile"() {
		given: "Application is running with test profile"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		when: "Application configuration is loaded"
		// No need to run the application, just verify configuration loading

		then: "Configuration should match test environment values"
		properties.getEnv() == "local" // Test environment uses local for LocalStack
		properties.getAws().getRegion() == "ap-northeast-1"
		properties.getAws().getAccessKeyId() == "test-access-key"
		properties.getAws().getSecretAccessKey() == "test-secret-key"
		properties.getAws().getS3().getBucketName() == "abijson-local-bucket"
		properties.getSubscription().getCheckInterval() == "1000" // Faster for tests
		!properties.isEagerStart() // Should be false for tests

		and: "LocalStack endpoint should be configured for test"
		properties.getAws().getDynamodb().getEndpoint().startsWith("http://localhost:")

		and: "Environment constants should match test environment"
		TEST_BUCKET == "abijson-local-bucket"
		EVENTS_TABLE == "local-Events"
		BLOCK_HEIGHT_TABLE == "local-BlockHeight"

		and: "Configuration should match constants"
		properties.getAws().getS3().getBucketName() == TEST_BUCKET
		properties.getAws().getDynamodb().getEventsTableName() == EVENTS_TABLE
		properties.getAws().getDynamodb().getBlockHeightTableName() == BLOCK_HEIGHT_TABLE
	}
}
